# Ryvyl BI Helper

A high-performance Node.js API service for business intelligence data processing and comparison operations. This service specializes in matching and reconciling financial balance data using advanced string similarity algorithms.

## Overview

The Ryvyl BI Helper is designed to process large datasets of financial balance records and perform intelligent matching against reference data sources. It uses optimized Jaro-Winkler string similarity algorithms with caching to efficiently compare merchant names, PSP (Payment Service Provider) names, and other financial data points.

## Key Features

- **High-Performance Data Comparison**: Processes large datasets with optimized chunking and caching strategies
- **String Similarity Matching**: Uses Jaro-Winkler algorithm for intelligent fuzzy matching of merchant and PSP names
- **RESTful API**: Clean Express.js-based API with comprehensive error handling
- **Scalable Architecture**: Built with TypeScript, featuring modular design and reusable components
- **Production-Ready**: Includes security middleware (Helmet), CORS support, and comprehensive logging
- **Integrated Logging**: Winston-based logging with Loggly integration for monitoring and debugging

## API Endpoints

### Health Check
```
GET /
```
Returns server health status.

### Compare All-to-All
```
POST /compare/all-to-all
```
Performs comprehensive comparison between target balance records and source merchant data.

**Request Body:**
```json
{
  "target": [
    {
      "date": "2024-01-01",
      "currency": "EUR",
      "pspName": "Example PSP",
      "companyName": "Example Company",
      "companyGroup": "Example Group",
      "merchantName": "Example Merchant",
      "merchantNo": "12345",
      "clientStatus": "active",
      "minimumFloat": 1000,
      "mcc": "5411",
      "toCurrency": "EUR",
      "rate": 1.0,
      "currentSettlementBalanceEUR": 5000,
      "currentRollingReserveBalanceEUR": 1000,
      "securityBalanceEUR": 500,
      "grandTotalInEur": 6500,
      "countryOfIncorporation": "DE",
      "type": "settlement"
    }
  ],
  "source": [
    {
      "psp": "Example PSP",
      "merchant": "Example Company Ltd",
      "descriptor": "EXAMPLE*COMPANY"
    }
  ]
}
```

**Response:**
Returns the target array with updated `pspName` and `companyName` fields based on best matches from the source data (minimum 90% similarity threshold).

## Technology Stack

- **Runtime**: Node.js (≥16.0.0)
- **Language**: TypeScript
- **Framework**: Express.js
- **Database**: MongoDB (via Mongoose)
- **Logging**: Winston with Loggly integration
- **Security**: Helmet, CORS
- **Development**: ts-node-dev, ESLint, Prettier
- **Process Management**: Husky for git hooks, lint-staged

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd ryvyl-bi-helper
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   Copy the example environment file and configure your settings:
   ```bash
   cp submodules/ryvyl-commons/.env.example .env
   ```

   Required environment variables:
   ```env
   ENV=DEV
   PORT=46001

   # Database
   MONGODB_URI=<your_mongodb_connection_string>
   MONGODB_DATABASE_NAME=<your_database_name>

   # Logging
   LOG_LEVEL=debug
   LOGGER_UPLOAD_LOGS=true
   LOGGER_SUBDOMAIN=<your_loggly_subdomain>
   LOGGER_TOKEN=<your_loggly_token>

   # Authentication (if using auth middleware)
   JWT_PUBLIC_KEY=<your_jwt_public_key>
   JWT_SECRET_KEY=<your_jwt_secret_key>
   JWT_ALGORITHM=ES512
   ```

## Usage

### Development
```bash
npm start
```
Starts the development server with hot reload on port 46001 (or configured PORT).

### Production Build
```bash
npm run build
npm run prod
```

### Code Formatting
```bash
npm run format
```

## Architecture

The application follows a modular architecture:

```
src/
├── app.ts              # Express app configuration
├── index.ts            # Server entry point
├── config/             # Configuration files
│   ├── config.ts       # Environment configuration
│   └── logger.ts       # BI-specific logger setup
├── controllers/        # Request handlers
│   └── comparisonController.ts
├── middlewares/        # Custom middleware
│   └── errorHandler.ts
├── models/             # Data models and interfaces
│   └── interfaces/
│       └── balances.ts
└── routes/             # API route definitions
    └── routes.ts
```

## Performance Optimizations

- **Chunked Processing**: Large datasets are processed in configurable chunks (default: 100 records)
- **String Comparison Caching**: Repeated string comparisons are cached to avoid recalculation
- **Early Exit Optimization**: Processing stops when perfect matches (100% similarity) are found
- **Asynchronous Processing**: Uses `setImmediate()` to yield control between chunks
- **Configurable Timeouts**: Server timeout set to 10 minutes for long-running operations

## Dependencies

### Core Dependencies
- `express` - Web framework
- `mongoose` - MongoDB object modeling
- `winston` - Logging library
- `cors` - Cross-origin resource sharing
- `helmet` - Security middleware
- `dotenv` - Environment variable management

### Development Dependencies
- `typescript` - Type checking and compilation
- `ts-node-dev` - Development server with hot reload
- `eslint` - Code linting
- `prettier` - Code formatting
- `husky` - Git hooks

## Submodules

This project includes the `ryvyl-commons` submodule which provides:
- Authentication services (JWT)
- Centralized logging configuration
- Database connectivity utilities
- Validation middleware
- Common utilities and helpers

## Contributing

1. Ensure Node.js version ≥16.0.0
2. Follow the existing code style (ESLint + Prettier)
3. Add appropriate tests for new features
4. Update documentation as needed

## License

ISC License - see package.json for details.

## Author

Encorp.io
