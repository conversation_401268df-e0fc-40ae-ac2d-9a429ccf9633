export interface Balance {
  date: string;
  currency: string;
  pspName: string;
  companyName: string;
  companyGroup: string;
  merchantName: string;
  merchantNo: string;
  clientStatus: string;
  minimumFloat: number;
  mcc: string;
  toCurrency: string;
  rate: number;
  currentSettlementBalanceEUR: number;
  currentRollingReserveBalanceEUR: number;
  securityBalanceEUR: number;
  grandTotalInEur: number;
  countryOfIncorporation: string;
  type: string;
  notes?: string;
}

export interface MidOur {
  psp: string;
  merchant: string;
  descriptor: string;
}
