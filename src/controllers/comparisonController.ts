import { Request, Response } from 'express';
import { Balance, MidOur } from '../models/interfaces/balances';
import { biLogger } from '../config/logger';

// Cache for string comparisons to avoid recalculating
const accuracyCache = new Map<string, number>();

export async function compareAllToAll(req: Request, res: Response): Promise<void> {
  const { target, source } = req.body;
  const startTime = Date.now();

  // Process in larger chunks for better performance
  const chunkSize = 100; // Increased chunk size
  const matchResults: Balance[] = [];

  for (let i = 0; i < target.length; i += chunkSize) {
    const chunk = (target as Balance[]).slice(i, i + chunkSize);

    const chunkResults = chunk.map((targetItem: Balance, chunkIndex: number) => {
      const globalIndex = i + chunkIndex;
      let bestMatch: any = null;
      let bestScore = 0;

      // Early exit optimization - stop when we find a perfect match
      for (const sourceItem of source as MidOur[]) {
        const pspAccuracy = getCachedAccuracy(targetItem.pspName || '', sourceItem.psp || '');
        const merchantAccuracy = getCachedAccuracy(targetItem.companyName || '', sourceItem.merchant || '');

        // Early exit if neither field meets threshold
        if (pspAccuracy < 0.9 && merchantAccuracy < 0.9) {
          continue;
        }

        const combinedScore = Math.max(pspAccuracy, merchantAccuracy);

        if (combinedScore > bestScore) {
          bestScore = combinedScore;
          bestMatch = sourceItem;

          // Early exit for perfect matches
          if (combinedScore >= 1.0) {
            break;
          }
        }
      }

      // Create updated Balance with replaced pspName and companyName from best match
      const updatedTargetItem: Balance = {
        ...targetItem, // Preserve all original target data
        // Replace pspName and companyName with best match from source
        pspName:
          bestMatch && getCachedAccuracy(targetItem.pspName || '', bestMatch.psp || '') >= 0.9
            ? bestMatch.psp
            : targetItem.pspName,
        companyName:
          bestMatch && getCachedAccuracy(targetItem.companyName || '', bestMatch.merchant || '') >= 0.9
            ? bestMatch.merchant
            : targetItem.companyName
      };

      return updatedTargetItem;
    });

    matchResults.push(...chunkResults);

    // Progress logging
    if (i % 200 === 0) {
      const elapsed = Date.now() - startTime;
      const progress = (((i + chunkSize) / target.length) * 100).toFixed(1);
      biLogger.info(`Progress: ${progress}% (${elapsed}ms elapsed)`);
    }

    // Yield control to event loop between chunks
    await new Promise((resolve) => setImmediate(resolve));
  }

  const totalTime = Date.now() - startTime;
  console.log(`Completed ${target.length} comparisons in ${totalTime}ms`);
  console.log(`Cache size: ${accuracyCache.size} entries`);

  res.status(200).json(matchResults);
  return;
}

// Cached accuracy function to avoid recalculating same string pairs
function getCachedAccuracy(s1: string, s2: string): number {
  const key = `${s1}|${s2}`;

  if (accuracyCache.has(key)) {
    return accuracyCache.get(key)!;
  }

  const accuracy = getAccuracy(s1, s2);
  accuracyCache.set(key, accuracy);
  return accuracy;
}

function jaro(s1: string, s2: string): number {
  var s1_len = s1.length;
  var s2_len = s2.length;
  if (s1_len === 0 && s2_len === 0) return 1.0;
  var match_distance = Math.floor(Math.max(s1_len, s2_len) / 2) - 1;
  var s1_matches = new Array(s1_len).fill(false);
  var s2_matches = new Array(s2_len).fill(false);
  var matches = 0;
  var transpositions = 0;
  for (var i = 0; i < s1_len; i++) {
    var start = Math.max(0, i - match_distance);
    var end = Math.min(i + match_distance + 1, s2_len);
    for (var j = start; j < end; j++) {
      if (s2_matches[j]) continue;
      if (s1[i] !== s2[j]) continue;
      s1_matches[i] = true;
      s2_matches[j] = true;
      matches++;
      break;
    }
  }
  if (matches === 0) return 0.0;
  var k = 0;
  for (var i = 0; i < s1_len; i++) {
    if (!s1_matches[i]) continue;
    while (!s2_matches[k]) k++;
    if (s1[i] !== s2[k]) transpositions++;
    k++;
  }
  return (matches / s1_len + matches / s2_len + (matches - transpositions / 2) / matches) / 3;
}

function getAccuracy(s1: string, s2: string): number {
  var jaro_sim = jaro(s1, s2);
  var prefix = 0;
  var max_prefix = 4;
  for (var i = 0; i < Math.min(s1.length, s2.length); i++) {
    if (s1[i] === s2[i]) prefix++;
    else break;
  }
  prefix = Math.min(prefix, max_prefix);
  return jaro_sim + 0.1 * prefix * (1 - jaro_sim);
}
